{"name": "@aiverify/moonshot", "version": "0.5.0", "private": true, "scripts": {"postinstall": "command -v poetry >/dev/null 2>&1 && poetry install || echo 'Poetry not installed'; ln -sf ../../../packages/moonshot-data moonshot/data", "dev": "PYTHONPATH=. poetry run python -m moonshot web-api", "build": "docker build -t ${IMAGE_NAME:-moonshot}:${IMAGE_TAG:-latest} -f Dockerfile ../..", "test": "echo 'Add test script here'", "lint": "echo 'Add lint script here'", "shell": "poetry shell", "fix-ssl-certs-error": "echo '' >> $(poetry run python -m certifi) && cat ~/.docker/certs.d/Cloudflare_CA.pem >> $(poetry run python -m certifi)", "requirements": "poetry export --without-hashes -f requirements.txt -o requirements.txt", "git:update": "git checkout main && git pull"}}